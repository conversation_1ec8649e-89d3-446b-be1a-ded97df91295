'use client';

import { useState, useEffect, useCallback } from 'react';
import {
	<PERSON><PERSON>,
	<PERSON>,
	CardContent,
	CardHeader,
	CardTitle,
	Input,
	LoadingSpinner,
	Badge,
	Select,
	SelectContent,
	SelectItem,
	SelectTrigger,
	SelectValue,
} from '@/components/ui';
import { useToast } from '@/contexts/toast-context';
import {
	Search,
	RefreshCw,
	UserPlus,
	Edit,
	Ban,
	CheckCircle,
	Shield,
	User,
	Key,
	Trash2,
	AlertTriangle,
	CheckSquare,
	Square,
} from 'lucide-react';
import { ChangeUserPasswordForm } from './change-user-password-form';
import { CreateUserForm } from './create-user-form';
import { EditUserForm } from './edit-user-form';
import { Role } from '@prisma/client';

interface UserData {
	id: string;
	username: string | null;
	provider: string;
	provider_id: string;
	role: Role;
	disabled: boolean;
	created_at: string;
	email?: string;
	name?: string;
}

interface UsersResponse {
	users: UserData[];
	total: number;
	page: number;
	limit: number;
}

export function EnhancedUserManagement() {
	const [users, setUsers] = useState<UserData[]>([]);
	const [loading, setLoading] = useState(false);
	const [refreshing, setRefreshing] = useState(false);
	const [searchTerm, setSearchTerm] = useState('');
	const [currentPage, setCurrentPage] = useState(1);
	const [totalPages, setTotalPages] = useState(1);
	const [totalCount, setTotalCount] = useState(0);
	const [selectedUsers, setSelectedUsers] = useState<Set<string>>(new Set());
	const [bulkDeleting, setBulkDeleting] = useState(false);
	const [updatingUsers, setUpdatingUsers] = useState<Set<string>>(new Set());

	// Filter and sort states
	const [filterProvider, setFilterProvider] = useState<string>('all');
	const [filterStatus, setFilterStatus] = useState<string>('all');
	const [filterRole, setFilterRole] = useState<string>('all');
	const [sortBy, setSortBy] = useState<'created_at' | 'username'>('created_at');
	const [sortOrder, setSortOrder] = useState<'asc' | 'desc'>('desc');

	// Form states
	const [showCreateForm, setShowCreateForm] = useState(false);
	const [showEditForm, setShowEditForm] = useState(false);
	const [showDeleteDialog, setShowDeleteDialog] = useState(false);
	const [selectedUser, setSelectedUser] = useState<UserData | null>(null);
	const [changePasswordUser, setChangePasswordUser] = useState<UserData | null>(null);

	const { showSuccess, showError } = useToast();
	const limit = 20;

	const fetchUsers = useCallback(
		async (page = 1, search = '', isRefresh = false) => {
			if (isRefresh) {
				setRefreshing(true);
			} else {
				setLoading(true);
			}

			try {
				const params = new URLSearchParams({
					page: page.toString(),
					limit: limit.toString(),
				});

				if (search) params.append('search', search);
				if (filterProvider && filterProvider !== 'all')
					params.append('provider', filterProvider);
				if (filterStatus && filterStatus !== 'all') params.append('status', filterStatus);
				if (filterRole && filterRole !== 'all') params.append('role', filterRole);
				if (sortBy) params.append('sortBy', sortBy);
				if (sortOrder) params.append('sortOrder', sortOrder);

				const response = await fetch(`/api/admin/users?${params}`, {
					credentials: 'include',
				});

				if (response.ok) {
					const data = await response.json();
					if (data.success) {
						const result: UsersResponse = data.data;
						setUsers(result.users);
						setTotalCount(result.total);
						setTotalPages(Math.ceil(result.total / limit));
						setCurrentPage(result.page);
					} else {
						throw new Error(data.error || 'Failed to fetch users');
					}
				} else {
					throw new Error('Failed to fetch users');
				}
			} catch (error) {
				showError(new Error('Failed to fetch users'));
			} finally {
				setLoading(false);
				setRefreshing(false);
			}
		},
		[showError, filterProvider, filterStatus, filterRole, sortBy, sortOrder]
	);

	useEffect(() => {
		fetchUsers(currentPage, searchTerm);
	}, [
		currentPage,
		searchTerm,
		fetchUsers,
		filterProvider,
		filterStatus,
		filterRole,
		sortBy,
		sortOrder,
	]);

	const handleSearch = () => {
		setCurrentPage(1);
		setSelectedUsers(new Set());
		fetchUsers(1, searchTerm);
	};

	const handleRefresh = () => {
		setSelectedUsers(new Set());
		fetchUsers(currentPage, searchTerm, true);
	};

	const handleSelectAll = () => {
		if (selectedUsers.size === users.length) {
			setSelectedUsers(new Set());
		} else {
			setSelectedUsers(new Set(users.map((u) => u.id)));
		}
	};

	const handleSelectUser = (userId: string) => {
		const newSelected = new Set(selectedUsers);
		if (newSelected.has(userId)) {
			newSelected.delete(userId);
		} else {
			newSelected.add(userId);
		}
		setSelectedUsers(newSelected);
	};

	const handleCreateUser = () => {
		setShowCreateForm(true);
	};

	const handleEditUser = (user: UserData) => {
		setSelectedUser(user);
		setShowEditForm(true);
	};

	const handleDeleteUser = (user: UserData) => {
		setSelectedUser(user);
		setShowDeleteDialog(true);
	};

	const confirmDeleteUser = async () => {
		if (!selectedUser) return;

		setUpdatingUsers((prev) => new Set(prev).add(selectedUser.id));

		try {
			const response = await fetch(`/api/admin/users/${selectedUser.id}`, {
				method: 'DELETE',
			});

			const data = await response.json();

			if (response.ok && data.success) {
				showSuccess('User deleted successfully');
				setSelectedUsers((prev) => {
					const newSet = new Set(prev);
					newSet.delete(selectedUser.id);
					return newSet;
				});
				fetchUsers(currentPage, searchTerm);
			} else {
				throw new Error(data.error || 'Failed to delete user');
			}
		} catch (error) {
			showError(new Error('Failed to delete user'));
		} finally {
			setUpdatingUsers((prev) => {
				const newSet = new Set(prev);
				newSet.delete(selectedUser.id);
				return newSet;
			});
			setShowDeleteDialog(false);
			setSelectedUser(null);
		}
	};

	const bulkDeleteUsers = async () => {
		if (selectedUsers.size === 0) return;

		setBulkDeleting(true);
		try {
			const response = await fetch('/api/admin/users/bulk-delete', {
				method: 'POST',
				headers: {
					'Content-Type': 'application/json',
				},
				body: JSON.stringify({
					userIds: Array.from(selectedUsers),
				}),
				credentials: 'include',
			});

			if (response.ok) {
				const data = await response.json();
				if (data.success) {
					showSuccess(data.message);
					setSelectedUsers(new Set());
					fetchUsers(currentPage, searchTerm);
				} else {
					throw new Error(data.error || 'Failed to bulk delete users');
				}
			} else {
				throw new Error('Failed to bulk delete users');
			}
		} catch (error) {
			showError(new Error('Failed to bulk delete users'));
		} finally {
			setBulkDeleting(false);
		}
	};

	const updateUserRole = async (userId: string, newRole: Role) => {
		setUpdatingUsers((prev) => new Set(prev).add(userId));

		try {
			const response = await fetch('/api/admin/users', {
				method: 'PATCH',
				headers: {
					'Content-Type': 'application/json',
				},
				body: JSON.stringify({
					userId,
					role: newRole,
				}),
			});

			const data = await response.json();

			if (response.ok && data.success) {
				setUsers((prev) =>
					prev.map((user) => (user.id === userId ? { ...user, role: newRole } : user))
				);
				showSuccess(`User role updated to ${newRole}`);
			} else {
				throw new Error(data.error || 'Failed to update user role');
			}
		} catch (error) {
			showError(new Error('Failed to update user role'));
		} finally {
			setUpdatingUsers((prev) => {
				const newSet = new Set(prev);
				newSet.delete(userId);
				return newSet;
			});
		}
	};

	const toggleUserStatus = async (userId: string, currentDisabled: boolean) => {
		setUpdatingUsers((prev) => new Set(prev).add(userId));

		try {
			const response = await fetch('/api/admin/users', {
				method: 'PATCH',
				headers: {
					'Content-Type': 'application/json',
				},
				body: JSON.stringify({
					userId,
					disabled: !currentDisabled,
				}),
			});

			const data = await response.json();

			if (response.ok && data.success) {
				setUsers((prev) =>
					prev.map((user) =>
						user.id === userId ? { ...user, disabled: !currentDisabled } : user
					)
				);
				showSuccess(`User ${!currentDisabled ? 'disabled' : 'enabled'} successfully`);
			} else {
				throw new Error(data.error || 'Failed to update user status');
			}
		} catch (error) {
			showError(new Error('Failed to update user status'));
		} finally {
			setUpdatingUsers((prev) => {
				const newSet = new Set(prev);
				newSet.delete(userId);
				return newSet;
			});
		}
	};

	const getStatusBadgeColor = (disabled: boolean) => {
		return disabled
			? 'bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-200'
			: 'bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200';
	};

	return (
		<div className="space-y-6">
			{/* Header */}
			<div className="flex justify-between items-center">
				<div>
					<h1 className="text-3xl font-bold text-gray-900 dark:text-white">
						User Management
					</h1>
					<p className="text-gray-600 dark:text-gray-400 mt-1">
						Manage user accounts and permissions
					</p>
				</div>
				<div className="flex space-x-2">
					<Button onClick={handleCreateUser} variant="default">
						<UserPlus className="h-4 w-4 mr-2" />
						Create User
					</Button>
					<Button onClick={handleRefresh} disabled={refreshing} variant="outline">
						<RefreshCw className={`h-4 w-4 mr-2 ${refreshing ? 'animate-spin' : ''}`} />
						Refresh
					</Button>
				</div>
			</div>

			{/* Stats */}
			<div className="grid grid-cols-1 md:grid-cols-4 gap-4">
				<Card>
					<CardContent className="pt-6">
						<div className="text-2xl font-bold">{totalCount}</div>
						<p className="text-xs text-muted-foreground">Total Users</p>
					</CardContent>
				</Card>
				<Card>
					<CardContent className="pt-6">
						<div className="text-2xl font-bold">
							{users.filter((u) => u.role === Role.ADMIN).length}
						</div>
						<p className="text-xs text-muted-foreground">Admins</p>
					</CardContent>
				</Card>
				<Card>
					<CardContent className="pt-6">
						<div className="text-2xl font-bold">
							{users.filter((u) => !u.disabled).length}
						</div>
						<p className="text-xs text-muted-foreground">Active</p>
					</CardContent>
				</Card>
				<Card>
					<CardContent className="pt-6">
						<div className="text-2xl font-bold">{selectedUsers.size}</div>
						<p className="text-xs text-muted-foreground">Selected</p>
					</CardContent>
				</Card>
			</div>

			{/* Search and Filters */}
			<Card>
				<CardContent className="pt-6">
					<div className="space-y-4">
						{/* Search */}
						<div className="flex gap-4 items-center">
							<div className="relative flex-1">
								<Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400 dark:text-gray-500" />
								<Input
									placeholder="Search users..."
									value={searchTerm}
									onChange={(e) => setSearchTerm(e.target.value)}
									onKeyDown={(e) => e.key === 'Enter' && handleSearch()}
									className="pl-10"
								/>
							</div>
							<Button onClick={handleSearch} variant="outline">
								Search
							</Button>
						</div>

						{/* Filters */}
						<div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 xl:grid-cols-6 gap-4">
							{/* Provider Filter */}
							<div>
								<div className="text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
									Provider
								</div>
								<Select value={filterProvider} onValueChange={setFilterProvider}>
									<SelectTrigger className="w-full">
										<SelectValue placeholder="All Providers" />
									</SelectTrigger>
									<SelectContent>
										<SelectItem value="all">All Providers</SelectItem>
										<SelectItem value="TELEGRAM">Telegram</SelectItem>
										<SelectItem value="GOOGLE">Google</SelectItem>
										<SelectItem value="USERNAME_PASSWORD">
											Username/Password
										</SelectItem>
									</SelectContent>
								</Select>
							</div>

							{/* Status Filter */}
							<div>
								<div className="text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
									Status
								</div>
								<Select value={filterStatus} onValueChange={setFilterStatus}>
									<SelectTrigger className="w-full">
										<SelectValue placeholder="All Status" />
									</SelectTrigger>
									<SelectContent>
										<SelectItem value="all">All Status</SelectItem>
										<SelectItem value="active">Active</SelectItem>
										<SelectItem value="disabled">Disabled</SelectItem>
									</SelectContent>
								</Select>
							</div>

							{/* Role Filter */}
							<div>
								<div className="text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
									Role
								</div>
								<Select value={filterRole} onValueChange={setFilterRole}>
									<SelectTrigger className="w-full">
										<SelectValue placeholder="All Roles" />
									</SelectTrigger>
									<SelectContent>
										<SelectItem value="all">All Roles</SelectItem>
										<SelectItem value="USER">User</SelectItem>
										<SelectItem value="ADMIN">Admin</SelectItem>
									</SelectContent>
								</Select>
							</div>

							{/* Sort By */}
							<div>
								<div className="text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
									Sort By
								</div>
								<Select
									value={sortBy}
									onValueChange={(value: 'created_at' | 'username') =>
										setSortBy(value)
									}
								>
									<SelectTrigger className="w-full">
										<SelectValue />
									</SelectTrigger>
									<SelectContent>
										<SelectItem value="created_at">Created Date</SelectItem>
										<SelectItem value="username">Username</SelectItem>
									</SelectContent>
								</Select>
							</div>

							{/* Sort Order */}
							<div>
								<div className="text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
									Order
								</div>
								<Select
									value={sortOrder}
									onValueChange={(value: 'asc' | 'desc') => setSortOrder(value)}
								>
									<SelectTrigger className="w-full">
										<SelectValue />
									</SelectTrigger>
									<SelectContent>
										<SelectItem value="desc">Newest First</SelectItem>
										<SelectItem value="asc">Oldest First</SelectItem>
									</SelectContent>
								</Select>
							</div>

							{/* Clear Filters */}
							<div className="flex items-end">
								<Button
									variant="outline"
									onClick={() => {
										setFilterProvider('all');
										setFilterStatus('all');
										setFilterRole('all');
										setSortBy('created_at');
										setSortOrder('desc');
										setSearchTerm('');
									}}
									className="w-full"
								>
									Clear Filters
								</Button>
							</div>
						</div>
					</div>
				</CardContent>
			</Card>

			{/* Bulk Actions */}
			{selectedUsers.size > 0 && (
				<Card className="border-orange-200 bg-orange-50 dark:border-orange-800 dark:bg-orange-950">
					<CardContent className="pt-6">
						<div className="flex items-center justify-between">
							<div className="flex items-center gap-2">
								<AlertTriangle className="h-4 w-4 text-orange-600" />
								<span className="text-sm font-medium">
									{selectedUsers.size} user(s) selected
								</span>
							</div>
							<div className="flex gap-2">
								<Button
									onClick={bulkDeleteUsers}
									disabled={bulkDeleting}
									variant="destructive"
									size="sm"
								>
									{bulkDeleting ? (
										<>
											<LoadingSpinner className="h-4 w-4 mr-2" />
											Deleting...
										</>
									) : (
										<>
											<Trash2 className="h-4 w-4 mr-2" />
											Delete Selected
										</>
									)}
								</Button>
							</div>
						</div>
					</CardContent>
				</Card>
			)}

			{/* Users Cards */}
			<Card>
				<CardHeader>
					<CardTitle className="flex items-center justify-between">
						<span>Users</span>
						<div className="flex items-center gap-2">
							<Button
								onClick={handleSelectAll}
								variant="outline"
								size="sm"
								className="flex items-center gap-2"
							>
								{selectedUsers.size === users.length ? (
									<CheckSquare className="h-4 w-4" />
								) : (
									<Square className="h-4 w-4" />
								)}
								Select All
							</Button>
						</div>
					</CardTitle>
				</CardHeader>
				<CardContent>
					{loading ? (
						<div className="flex items-center justify-center h-64">
							<LoadingSpinner className="h-8 w-8" />
						</div>
					) : (
						<div className="space-y-4">
							{users.map((user) => (
								<Card
									key={user.id}
									className={`transition-all duration-200 hover:shadow-md ${
										selectedUsers.has(user.id)
											? 'ring-2 ring-blue-500 bg-blue-50 dark:bg-blue-950'
											: 'hover:shadow-lg'
									}`}
								>
									<CardContent className="p-4">
										<div className="flex flex-col lg:flex-row lg:items-center gap-4">
											{/* Selection and User Info */}
											<div className="flex items-center gap-3 flex-1 min-w-0">
												{/* Selection Checkbox */}
												<button
													onClick={() => handleSelectUser(user.id)}
													className="flex items-center justify-center w-5 h-5 border rounded bg-white dark:bg-gray-800 shadow-sm flex-shrink-0"
												>
													{selectedUsers.has(user.id) ? (
														<CheckSquare className="h-4 w-4 text-blue-600" />
													) : (
														<Square className="h-4 w-4 text-gray-400" />
													)}
												</button>

												{/* User Icon and Info */}
												<div className="flex items-center gap-3 flex-1 min-w-0">
													{user.role === Role.ADMIN ? (
														<Shield className="h-6 w-6 text-red-500 flex-shrink-0" />
													) : (
														<User className="h-6 w-6 text-gray-400 flex-shrink-0" />
													)}
													<div className="min-w-0 flex-1">
														<h3 className="font-medium text-gray-900 dark:text-white truncate">
															{user.username || 'Anonymous'}
														</h3>
														<p className="text-sm text-gray-500 dark:text-gray-400">
															ID: {user.id.slice(0, 8)}...
														</p>
													</div>
												</div>
											</div>

											{/* User Details */}
											<div className="flex flex-wrap items-center gap-4 lg:gap-6">
												{/* Provider */}
												<div className="flex flex-col items-start lg:items-center">
													<span className="text-xs text-gray-500 dark:text-gray-400 mb-1">
														Provider
													</span>
													<Badge variant="outline" className="text-xs">
														{user.provider}
													</Badge>
												</div>

												{/* Role */}
												<div className="flex flex-col items-start lg:items-center min-w-[120px]">
													<span className="text-xs text-gray-500 dark:text-gray-400 mb-1">
														Role
													</span>
													<Select
														value={user.role}
														onValueChange={(value: Role) =>
															updateUserRole(user.id, value)
														}
														disabled={updatingUsers.has(user.id)}
													>
														<SelectTrigger className="w-full h-8 text-sm">
															<SelectValue />
														</SelectTrigger>
														<SelectContent>
															<SelectItem value={Role.USER}>
																User
															</SelectItem>
															<SelectItem value={Role.ADMIN}>
																Admin
															</SelectItem>
														</SelectContent>
													</Select>
												</div>

												{/* Status */}
												<div className="flex flex-col items-start lg:items-center">
													<span className="text-xs text-gray-500 dark:text-gray-400 mb-1">
														Status
													</span>
													<Badge
														className={getStatusBadgeColor(
															user.disabled
														)}
													>
														{user.disabled ? 'Disabled' : 'Active'}
													</Badge>
												</div>

												{/* Created Date */}
												<div className="flex flex-col items-start lg:items-center">
													<span className="text-xs text-gray-500 dark:text-gray-400 mb-1">
														Created
													</span>
													<span className="text-sm text-gray-600 dark:text-gray-400">
														{new Date(
															user.created_at
														).toLocaleDateString()}
													</span>
												</div>
											</div>

											{/* Action Buttons */}
											<div className="flex flex-wrap gap-2 lg:flex-nowrap lg:gap-1">
												{/* Edit Button */}
												<Button
													size="sm"
													variant="outline"
													onClick={() => handleEditUser(user)}
													disabled={updatingUsers.has(user.id)}
													title="Edit User"
												>
													<Edit className="h-3 w-3" />
												</Button>

												{/* Change Password Button - Only for USERNAME_PASSWORD users */}
												{user.provider === 'USERNAME_PASSWORD' && (
													<Button
														size="sm"
														variant="outline"
														onClick={() => setChangePasswordUser(user)}
														disabled={updatingUsers.has(user.id)}
														title="Change Password"
													>
														<Key className="h-3 w-3" />
													</Button>
												)}

												{/* Enable/Disable Button */}
												<Button
													size="sm"
													variant={
														user.disabled ? 'default' : 'destructive'
													}
													onClick={() =>
														toggleUserStatus(user.id, user.disabled)
													}
													disabled={updatingUsers.has(user.id)}
													title={
														user.disabled
															? 'Enable User'
															: 'Disable User'
													}
												>
													{updatingUsers.has(user.id) ? (
														<LoadingSpinner className="h-3 w-3" />
													) : user.disabled ? (
														<CheckCircle className="h-3 w-3" />
													) : (
														<Ban className="h-3 w-3" />
													)}
												</Button>

												{/* Delete Button */}
												<Button
													size="sm"
													variant="destructive"
													onClick={() => handleDeleteUser(user)}
													disabled={updatingUsers.has(user.id)}
													title="Delete User"
												>
													{updatingUsers.has(user.id) ? (
														<LoadingSpinner className="h-3 w-3" />
													) : (
														<Trash2 className="h-3 w-3" />
													)}
												</Button>
											</div>
										</div>
									</CardContent>
								</Card>
							))}
						</div>
					)}

					{/* Pagination */}
					{totalPages > 1 && (
						<div className="flex items-center justify-between mt-6">
							<div className="text-sm text-gray-600 dark:text-gray-400">
								Showing {(currentPage - 1) * limit + 1} to{' '}
								{Math.min(currentPage * limit, totalCount)} of {totalCount} users
							</div>
							<div className="flex gap-2">
								<Button
									onClick={() => setCurrentPage((prev) => Math.max(1, prev - 1))}
									disabled={currentPage === 1}
									variant="outline"
									size="sm"
								>
									Previous
								</Button>
								<span className="flex items-center px-3 py-1 text-sm">
									Page {currentPage} of {totalPages}
								</span>
								<Button
									onClick={() =>
										setCurrentPage((prev) => Math.min(totalPages, prev + 1))
									}
									disabled={currentPage === totalPages}
									variant="outline"
									size="sm"
								>
									Next
								</Button>
							</div>
						</div>
					)}

					{users.length === 0 && !loading && (
						<div className="text-center text-gray-500 dark:text-gray-400 py-8">
							No users found.
						</div>
					)}
				</CardContent>
			</Card>

			{/* Forms and Dialogs */}
			{showCreateForm && (
				<CreateUserForm
					onCancel={() => setShowCreateForm(false)}
					onSuccess={() => {
						setShowCreateForm(false);
						fetchUsers(currentPage, searchTerm);
					}}
				/>
			)}

			{showEditForm && selectedUser && (
				<EditUserForm
					user={selectedUser}
					onCancel={() => {
						setShowEditForm(false);
						setSelectedUser(null);
					}}
					onSuccess={() => {
						setShowEditForm(false);
						setSelectedUser(null);
						fetchUsers(currentPage, searchTerm);
					}}
				/>
			)}

			{changePasswordUser && (
				<ChangeUserPasswordForm
					userId={changePasswordUser.id}
					username={changePasswordUser.username || 'N/A'}
					onCancel={() => setChangePasswordUser(null)}
					onSuccess={() => {
						setChangePasswordUser(null);
						fetchUsers(currentPage, searchTerm);
					}}
				/>
			)}

			{/* Delete Confirmation Dialog */}
			{showDeleteDialog && selectedUser && (
				<div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
					<Card className="w-full max-w-md mx-4">
						<CardHeader>
							<CardTitle className="flex items-center gap-2 text-red-600">
								<AlertTriangle className="h-5 w-5" />
								Confirm Delete
							</CardTitle>
						</CardHeader>
						<CardContent className="space-y-4">
							<p className="text-gray-600 dark:text-gray-400">
								Are you sure you want to delete user{' '}
								<strong>{selectedUser.username || 'Anonymous'}</strong>? This action
								cannot be undone and will delete all associated data.
							</p>
							<div className="flex gap-2">
								<Button
									onClick={confirmDeleteUser}
									disabled={updatingUsers.has(selectedUser.id)}
									className="flex-1"
									variant="destructive"
								>
									{updatingUsers.has(selectedUser.id) ? (
										<>
											<LoadingSpinner className="h-4 w-4 mr-2" />
											Deleting...
										</>
									) : (
										'Delete User'
									)}
								</Button>
								<Button
									variant="outline"
									onClick={() => {
										setShowDeleteDialog(false);
										setSelectedUser(null);
									}}
									className="flex-1"
								>
									Cancel
								</Button>
							</div>
						</CardContent>
					</Card>
				</div>
			)}
		</div>
	);
}
